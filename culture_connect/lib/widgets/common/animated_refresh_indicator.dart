import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:culture_connect/utils/refresh_animation_utils.dart';

/// A custom refresh indicator with animated effects
class AnimatedRefreshIndicator extends StatefulWidget {
  /// The widget below this widget in the tree
  final Widget child;

  /// Called when the user has dragged the refresh indicator
  /// far enough to demonstrate that they want a refresh
  final RefreshCallback onRefresh;

  /// The distance from the top of the [child] at which the
  /// refresh indicator will settle after being dragged down
  final double displacement;

  /// The background color of the refresh indicator
  final Color? backgroundColor;

  /// The foreground color of the refresh indicator
  final Color? color;

  /// The type of animation to use for the refresh indicator
  final RefreshAnimationType animationType;

  /// Whether to trigger haptic feedback when the refresh starts
  final bool triggerHapticFeedback;

  /// The stroke width of the refresh indicator
  final double strokeWidth;

  /// Creates an animated refresh indicator
  const AnimatedRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 40.0,
    this.backgroundColor,
    this.color,
    this.animationType = RefreshAnimationType.circular,
    this.triggerHapticFeedback = true,
    this.strokeWidth = 3.0,
  });

  @override
  State<AnimatedRefreshIndicator> createState() =>
      _AnimatedRefreshIndicatorState();
}

class _AnimatedRefreshIndicatorState extends State<AnimatedRefreshIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _primaryAnimation;
  late Animation<double> _secondaryAnimation;
  late Animation<Color?> _colorAnimation;
  bool _animationsInitialized = false;

  @override
  void initState() {
    super.initState();

    // Create animation controller
    _controller = AnimationController(
      vsync: this,
      duration: RefreshAnimationUtils.getDurationForType(widget.animationType),
    );

    // Note: _initializeAnimations() moved to didChangeDependencies()
    // to avoid Theme.of(context) access before widget tree is built
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize animations here where Theme.of(context) is safe to use
    if (!_animationsInitialized) {
      _initializeAnimations();
      _controller.repeat();
      _animationsInitialized = true;
    }
  }

  @override
  void didUpdateWidget(AnimatedRefreshIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the animation type changed, update the animations
    if (oldWidget.animationType != widget.animationType) {
      _controller.duration =
          RefreshAnimationUtils.getDurationForType(widget.animationType);
      if (_animationsInitialized) {
        _initializeAnimations();
        _controller.reset();
        _controller.repeat();
      }
    }
  }

  void _initializeAnimations() {
    final curve = RefreshAnimationUtils.getCurveForType(widget.animationType);

    switch (widget.animationType) {
      case RefreshAnimationType.circular:
        _primaryAnimation = RefreshAnimationUtils.createRotationAnimation(
          _controller,
          curve: curve,
        );
        _secondaryAnimation = RefreshAnimationUtils.createScaleAnimation(
          _controller,
          begin: 1.0,
          end: 1.0,
          curve: curve,
        );
        break;
      case RefreshAnimationType.liquid:
        _primaryAnimation = RefreshAnimationUtils.createLiquidAnimation(
          _controller,
          begin: 0.0,
          end: 1.0,
        );
        _secondaryAnimation = RefreshAnimationUtils.createScaleAnimation(
          _controller,
          begin: 0.8,
          end: 1.2,
          curve: Curves.easeInOut,
        );
        break;
      case RefreshAnimationType.bounce:
        _primaryAnimation = RefreshAnimationUtils.createBounceAnimation(
          _controller,
          begin: 0.0,
          end: 1.0,
        );
        _secondaryAnimation = RefreshAnimationUtils.createScaleAnimation(
          _controller,
          begin: 0.5,
          end: 1.0,
          curve: Curves.bounceOut,
        );
        break;
      case RefreshAnimationType.flip:
        _primaryAnimation = RefreshAnimationUtils.createFlipAnimation(
          _controller,
          begin: 0.0,
          end: 1.0,
        );
        _secondaryAnimation = RefreshAnimationUtils.createRotationAnimation(
          _controller,
          begin: 0.0,
          end: math.pi,
          curve: Curves.easeInOutBack,
        );
        break;
      case RefreshAnimationType.pulse:
        _primaryAnimation = RefreshAnimationUtils.createPulseAnimation(
          _controller,
          begin: 1.0,
          end: 1.3,
        );
        _secondaryAnimation = RefreshAnimationUtils.createPulseAnimation(
          _controller,
          begin: 1.0,
          end: 0.7,
        );
        break;
    }

    // Create color animation
    final theme = Theme.of(context);
    final defaultColor = widget.color ?? theme.colorScheme.primary;

    _colorAnimation = RefreshAnimationUtils.createColorAnimation(
      _controller,
      begin: defaultColor,
      end: defaultColor.withAlpha(179), // 0.7 * 255 = 178.5, rounded to 179
      curve: curve,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      displacement: widget.displacement,
      backgroundColor: widget.backgroundColor,
      color: Colors.transparent, // Make the default indicator transparent
      strokeWidth: 0, // Hide the default indicator
      notificationPredicate: (notification) {
        // Only respond to scroll notifications from the direct child
        return notification.depth == 0;
      },
      child: Stack(
        children: [
          widget.child,
          // Custom refresh indicator overlay
          Builder(
            builder: (context) {
              // Find the RefreshIndicator in the tree
              final RefreshIndicatorState? refreshIndicatorState =
                  context.findAncestorStateOfType<RefreshIndicatorState>();

              if (refreshIndicatorState == null) {
                return const SizedBox.shrink();
              }

              return NotificationListener<ScrollNotification>(
                onNotification: (notification) {
                  // Handle scroll notifications to control animation
                  if (notification is ScrollStartNotification) {
                    // Reset animation when scroll starts
                    if (!_controller.isAnimating) {
                      _controller.repeat();
                    }
                  }
                  return false; // Continue propagating the notification
                },
                child: AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: _RefreshIndicatorPainter(
                        animation: _primaryAnimation,
                        secondaryAnimation: _secondaryAnimation,
                        colorAnimation: _colorAnimation,
                        animationType: widget.animationType,
                        strokeWidth: widget.strokeWidth,
                      ),
                      child: const SizedBox.expand(),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// Custom painter for the refresh indicator
class _RefreshIndicatorPainter extends CustomPainter {
  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final Animation<Color?> colorAnimation;
  final RefreshAnimationType animationType;
  final double strokeWidth;

  _RefreshIndicatorPainter({
    required this.animation,
    required this.secondaryAnimation,
    required this.colorAnimation,
    required this.animationType,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = colorAnimation.value ?? Colors.blue
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, 40); // Position at the top center
    const radius = 15.0;

    switch (animationType) {
      case RefreshAnimationType.circular:
        _paintCircular(canvas, center, radius, paint);
        break;
      case RefreshAnimationType.liquid:
        _paintLiquid(canvas, center, radius, paint);
        break;
      case RefreshAnimationType.bounce:
        _paintBounce(canvas, center, radius, paint);
        break;
      case RefreshAnimationType.flip:
        _paintFlip(canvas, center, radius, paint);
        break;
      case RefreshAnimationType.pulse:
        _paintPulse(canvas, center, radius, paint);
        break;
    }
  }

  void _paintCircular(
      Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw a circular arc that rotates
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(animation.value);

    const double startAngle = -math.pi / 2;
    const double sweepAngle = math.pi * 1.5;

    canvas.drawArc(
      Rect.fromCircle(center: Offset.zero, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );

    canvas.restore();
  }

  void _paintLiquid(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw a liquid-like shape
    final path = Path();
    final scaledRadius = radius * secondaryAnimation.value;

    path.addOval(Rect.fromCircle(center: center, radius: scaledRadius));

    // Add wave effect
    final wavePath = Path();
    final waveHeight = 5.0 * animation.value;
    const waveWidth = 10.0;

    wavePath.moveTo(center.dx - scaledRadius, center.dy);

    for (double i = -scaledRadius; i <= scaledRadius; i += waveWidth) {
      wavePath.quadraticBezierTo(
        center.dx + i + waveWidth / 2,
        center.dy + waveHeight,
        center.dx + i + waveWidth,
        center.dy,
      );
    }

    // Combine paths
    path.addPath(wavePath, Offset.zero);

    canvas.drawPath(path, paint);
  }

  void _paintBounce(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw a bouncing circle
    final bouncedCenter = Offset(
      center.dx,
      center.dy + 10 * (1 - animation.value),
    );

    final scaledRadius = radius * secondaryAnimation.value;

    canvas.drawCircle(bouncedCenter, scaledRadius, paint);

    // Draw a shadow
    final shadowPaint = Paint()
      ..color = paint.color.withAlpha(77) // 0.3 * 255 = 76.5, rounded to 77
      ..style = PaintingStyle.fill;

    final shadowRadius = radius * 0.8 * (1 - animation.value);
    final shadowCenter = Offset(center.dx, center.dy + 20);

    canvas.drawOval(
      Rect.fromCenter(
        center: shadowCenter,
        width: shadowRadius * 2,
        height: shadowRadius * 0.5,
      ),
      shadowPaint,
    );
  }

  void _paintFlip(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw a flipping icon
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate(secondaryAnimation.value);

    // Draw a refresh icon
    final path = Path();
    final arrowSize = radius * 0.8;

    // Draw arrow
    path.moveTo(0, -arrowSize);
    path.lineTo(arrowSize * 0.6, -arrowSize * 0.4);
    path.lineTo(arrowSize * 0.3, -arrowSize * 0.4);
    path.lineTo(arrowSize * 0.3, arrowSize * 0.4);
    path.lineTo(-arrowSize * 0.3, arrowSize * 0.4);
    path.lineTo(-arrowSize * 0.3, -arrowSize * 0.4);
    path.lineTo(-arrowSize * 0.6, -arrowSize * 0.4);
    path.close();

    // Apply scale based on animation
    final scaleMatrix = Matrix4.identity()
      ..scale(1.0, math.cos(secondaryAnimation.value).abs());

    path.transform(scaleMatrix.storage);

    canvas.drawPath(path, paint);
    canvas.restore();
  }

  void _paintPulse(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw a pulsing circle
    final scaledRadius = radius * animation.value;

    // Draw main circle
    canvas.drawCircle(center, scaledRadius, paint);

    // Draw pulse waves
    for (int i = 1; i <= 3; i++) {
      final wavePaint = Paint()
        ..color = paint.color.withAlpha(
            (77 - (i * 26)).clamp(0, 255)) // 0.3 - (i * 0.1) converted to alpha
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth * (1 - (i * 0.2));

      final waveRadius = scaledRadius + (i * 5 * secondaryAnimation.value);

      canvas.drawCircle(center, waveRadius, wavePaint);
    }
  }

  @override
  bool shouldRepaint(_RefreshIndicatorPainter oldDelegate) {
    return animation.value != oldDelegate.animation.value ||
        secondaryAnimation.value != oldDelegate.secondaryAnimation.value ||
        colorAnimation.value != oldDelegate.colorAnimation.value ||
        animationType != oldDelegate.animationType ||
        strokeWidth != oldDelegate.strokeWidth;
  }
}
