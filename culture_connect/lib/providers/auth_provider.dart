import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/models/user_model.dart';

// Provider for the AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// Provider for the current authentication status
final authStatusProvider = StreamProvider<AuthStatus>((ref) {
  final authService = ref.watch(authServiceProvider);

  // Create a stream that emits the current status immediately, then follows auth changes
  return Stream<AuthStatus>.multi((controller) {
    // Emit current status immediately
    controller.add(authService.currentAuthStatus);

    // Listen to auth state changes
    final subscription = authService.authStateChanges.listen(
      (status) => controller.add(status),
      onError: (error) {
        // Emit unauthenticated as fallback on error
        controller.add(AuthStatus.unauthenticated);
      },
    );

    controller.onCancel = () => subscription.cancel();
  });
});

// Provider for the current user
final currentUserProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

// Provider for the current user ID
final currentUserIdProvider = Provider<String?>((ref) {
  final userAsync = ref.watch(currentUserProvider);
  return userAsync.when(
    data: (user) => user?.uid,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Provider for the current user model
final currentUserModelProvider = FutureProvider<UserModel?>((ref) async {
  final authService = ref.watch(authServiceProvider);
  return authService.currentUserModel;
});

// Auth state notifier
class AuthState {
  final AuthStatus status;
  final UserModel? user;
  final String? errorMessage;
  final bool isLoading;

  AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.errorMessage,
    this.isLoading = false,
  });

  AuthState copyWith({
    AuthStatus? status,
    UserModel? user,
    String? errorMessage,
    bool? isLoading,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// Auth state notifier provider
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(AuthState()) {
    // Listen to auth state changes
    _authService.authStateChanges.listen((status) {
      state = state.copyWith(status: status);
      if (status == AuthStatus.authenticated) {
        _loadUserData();
      }
    });
  }

  Future<void> _loadUserData() async {
    try {
      final user = await _authService.currentUserModel;
      state = state.copyWith(user: user);
    } catch (e) {
      // Enhanced error handling with specific Firebase error messages
      String errorMessage = 'Failed to load user data';
      if (e.toString().contains('unavailable')) {
        errorMessage = 'Service temporarily unavailable. Please try again.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection.';
      } else {
        errorMessage = 'Failed to load user data: $e';
      }
      state = state.copyWith(errorMessage: errorMessage);
    }
  }

  Future<void> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    required String dateOfBirth,
  }) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      await _authService.registerWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        dateOfBirth: dateOfBirth,
      );
      state = state.copyWith(
        isLoading: false,
        status: AuthStatus.verificationPending,
      );
    } on FirebaseAuthException catch (e) {
      String errorMsg;
      switch (e.code) {
        case 'weak-password':
          errorMsg = 'The password provided is too weak.';
          break;
        case 'email-already-in-use':
          errorMsg = 'An account already exists for this email.';
          break;
        case 'invalid-email':
          errorMsg = 'The email address is not valid.';
          break;
        case 'operation-not-allowed':
          errorMsg = 'Email/password accounts are not enabled.';
          break;
        default:
          errorMsg = 'Registration failed: ${e.message}';
      }
      state = state.copyWith(isLoading: false, errorMessage: errorMsg);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  Future<void> loginWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      await _authService.loginWithEmailAndPassword(
        email: email,
        password: password,
      );
      state = state.copyWith(isLoading: false);
    } on FirebaseAuthException catch (e) {
      String errorMsg;
      if (e.code == 'user-not-found' || e.code == 'wrong-password') {
        errorMsg = 'Invalid email or password.';
      } else {
        errorMsg = 'Login failed: ${e.message}';
      }
      state = state.copyWith(isLoading: false, errorMessage: errorMsg);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  Future<void> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      await _authService.signInWithGoogle();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Google sign-in failed: $e',
      );
    }
  }

  Future<void> signOut() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      await _authService.signOut();
      state = state.copyWith(
        isLoading: false,
        status: AuthStatus.unauthenticated,
        user: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Sign out failed: $e',
      );
    }
  }

  Future<void> sendPasswordResetEmail(String email) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      await _authService.sendPasswordResetEmail(email);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Password reset failed: $e',
      );
    }
  }

  Future<void> verifyEmail() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      final isVerified = await _authService.verifyEmail();
      state = state.copyWith(
        isLoading: false,
        status: isVerified
            ? AuthStatus.authenticated
            : AuthStatus.verificationPending,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Email verification check failed: $e',
      );
    }
  }

  Future<void> resendVerificationEmail() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      await _authService.resendVerificationEmail();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to resend verification email: $e',
      );
    }
  }
}
